package models

import (
	"claude-pilot/core/api"
	"claude-pilot/shared/styles"
	"fmt"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// SessionStats represents session statistics
type SessionStats struct {
	Total    int
	Active   int
	Inactive int
	Error    int
	Backend  string
}

// SummaryPanelModel represents the summary panel showing metrics
type SummaryPanelModel struct {
	client   *api.Client
	width    int
	sessions []*api.Session
	stats    SessionStats
}

// NewSummaryPanelModel creates a new summary panel model
func NewSummaryPanelModel(client *api.Client) *SummaryPanelModel {
	return &SummaryPanelModel{
		client: client,
		stats: SessionStats{
			Backend: client.GetBackend(),
		},
	}
}

// Init implements tea.Model
func (m *SummaryPanelModel) Init() tea.Cmd {
	return tea.Batch(
		m.refreshMetrics(),
		m.startPeriodicRefresh(),
	)
}

// Update implements tea.Model
func (m *SummaryPanelModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width

	case SessionsLoadedMsg:
		if msg.Error == nil {
			m.SetSessions(msg.Sessions)
		}

	case time.Time:
		// Auto-refresh timer - refresh data and restart timer
		return m, tea.Batch(
			m.refreshMetrics(),
			m.startPeriodicRefresh(),
		)
	}

	return m, nil
}

// View implements tea.Model
func (m *SummaryPanelModel) View() string {
	if m.width == 0 {
		return ""
	}

	return m.renderSummaryCards()
}

// renderSummaryCards renders summary cards with responsive layout using shared layout components
func (m *SummaryPanelModel) renderSummaryCards() string {
	// Calculate responsive width and size
	_, size := styles.GetResponsiveWidth(m.width)

	switch size {
	case "small":
		return m.renderCompactView()
	case "medium":
		return m.renderMediumView()
	default:
		return m.renderFullView()
	}
}

// renderCompactView renders a compact view for small screens
func (m *SummaryPanelModel) renderCompactView() string {
	// Single row with key metrics using simple styled text
	totalStyle := lipgloss.NewStyle().Foreground(styles.ClaudePrimary).Bold(true)
	activeStyle := lipgloss.NewStyle().Foreground(styles.SuccessColor).Bold(true)
	inactiveStyle := lipgloss.NewStyle().Foreground(styles.InfoColor).Bold(true)

	total := fmt.Sprintf("📊 %s %s", totalStyle.Render(fmt.Sprintf("%d", m.stats.Total)), "total")
	active := fmt.Sprintf("✅ %s %s", activeStyle.Render(fmt.Sprintf("%d", m.stats.Active)), "active")
	inactive := fmt.Sprintf("⏸️ %s %s", inactiveStyle.Render(fmt.Sprintf("%d", m.stats.Inactive)), "inactive")
	backend := styles.DimTextStyle.Render(fmt.Sprintf("%s backend", m.stats.Backend))

	return lipgloss.JoinHorizontal(
		lipgloss.Top,
		total,
		" • ",
		active,
		" • ",
		inactive,
		" • ",
		backend,
	)
}

// renderMediumView renders a medium view for medium screens using 2-row grid
func (m *SummaryPanelModel) renderMediumView() string {
	cardWidth := (m.width - 6) / 2 // 2 cards per row with spacing
	if cardWidth < 12 {
		return m.renderCompactView()
	}

	// Create bordered boxes for each metric
	totalBox := m.createMetricBox("📊 Total", fmt.Sprintf("%d", m.stats.Total), "sessions", styles.ClaudePrimary, cardWidth)
	activeBox := m.createMetricBox("✅ Active", fmt.Sprintf("%d", m.stats.Active), "running", styles.SuccessColor, cardWidth)
	inactiveBox := m.createMetricBox("⏸️ Inactive", fmt.Sprintf("%d", m.stats.Inactive), "inactive", styles.InfoColor, cardWidth)
	errorBox := m.createMetricBox("❌ Errors", fmt.Sprintf("%d", m.stats.Error), "error", styles.ErrorColor, cardWidth)

	// Create 2x2 grid layout
	topRow := lipgloss.JoinHorizontal(lipgloss.Top, totalBox, " ", activeBox)
	bottomRow := lipgloss.JoinHorizontal(lipgloss.Top, inactiveBox, " ", errorBox)

	return lipgloss.JoinVertical(lipgloss.Left, topRow, bottomRow)
}

// renderFullView renders the full view for large screens
func (m *SummaryPanelModel) renderFullView() string {
	cardWidth := (m.width - 12) / 4 // 4 cards with spacing
	if cardWidth < 12 {
		return m.renderMediumView()
	}

	// Create bordered boxes for each metric
	totalBox := m.createMetricBox("📊 Total", fmt.Sprintf("%d", m.stats.Total), "sessions", styles.ClaudePrimary, cardWidth)
	activeBox := m.createMetricBox("✅ Active", fmt.Sprintf("%d", m.stats.Active), "running", styles.SuccessColor, cardWidth)
	inactiveBox := m.createMetricBox("⏸️ Inactive", fmt.Sprintf("%d", m.stats.Inactive), "stopped", styles.WarningColor, cardWidth)
	errorBox := m.createMetricBox("❌ Errors", fmt.Sprintf("%d", m.stats.Error), "failed", styles.ErrorColor, cardWidth)

	return lipgloss.JoinHorizontal(
		lipgloss.Top,
		totalBox,
		" ",
		activeBox,
		" ",
		inactiveBox,
		" ",
		errorBox,
	)
}

// createMetricBox creates a bordered metric box with title, value, and label
func (m *SummaryPanelModel) createMetricBox(title, value, label string, color lipgloss.Color, width int) string {
	// Create styles
	titleStyle := lipgloss.NewStyle().
		Foreground(color).
		Bold(true).
		Align(lipgloss.Center).
		Width(width - 2)

	valueStyle := lipgloss.NewStyle().
		Foreground(color).
		Bold(true).
		Align(lipgloss.Center).
		Width(width - 2)

	labelStyle := lipgloss.NewStyle().
		Foreground(styles.DimTextStyle.GetForeground()).
		Align(lipgloss.Center).
		Width(width - 2)

	// Build content
	content := lipgloss.JoinVertical(
		lipgloss.Center,
		titleStyle.Render(title),
		valueStyle.Render(value),
		labelStyle.Render(label),
	)

	// Create bordered box
	boxStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(color).
		Padding(0, 1).
		Width(width).
		Align(lipgloss.Center)

	return boxStyle.Render(content)
}

// SetSessions updates the sessions and recalculates metrics
func (m *SummaryPanelModel) SetSessions(sessions []*api.Session) {
	m.sessions = sessions
	m.calculateStats()
}

// SetSize updates the panel size
func (m *SummaryPanelModel) SetSize(width, height int) {
	m.width = width
}

// calculateStats calculates summary statistics from sessions
func (m *SummaryPanelModel) calculateStats() {
	m.stats.Total = len(m.sessions)
	m.stats.Active = 0
	m.stats.Inactive = 0
	m.stats.Error = 0

	for _, session := range m.sessions {
		switch session.Status {
		case api.StatusActive:
			m.stats.Active++
		case api.StatusInactive:
			m.stats.Inactive++
		case api.StatusError:
			m.stats.Error++
		default:
			// Handle any other status as inactive
			m.stats.Inactive++
		}
	}
}

// refreshMetrics refreshes the metrics by loading sessions
func (m *SummaryPanelModel) refreshMetrics() tea.Cmd {
	return func() tea.Msg {
		sessions, err := m.client.ListSessions()
		return SessionsLoadedMsg{Sessions: sessions, Error: err}
	}
}

// startPeriodicRefresh starts a periodic refresh timer for real-time updates
func (m *SummaryPanelModel) startPeriodicRefresh() tea.Cmd {
	return tea.Tick(time.Second*5, func(t time.Time) tea.Msg {
		return t
	})
}

// GetStats returns current statistics
func (m *SummaryPanelModel) GetStats() SessionStats {
	return m.stats
}
