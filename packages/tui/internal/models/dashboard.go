package models

import (
	"claude-pilot/core/api"
	"claude-pilot/shared/styles"
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/help"
	"github.com/charmbracelet/bubbles/key"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// DashboardModel represents the main dashboard view
type DashboardModel struct {
	client *api.Client
	width  int
	height int

	// Child components
	summaryPanel *SummaryPanelModel
	sessionTable *SessionTableModel
	detailPanel  *DetailPanelModel
	createModal  *CreateModalModel

	// Bubbles help component
	help help.Model
	keys KeyMap

	// State
	focused         Component
	showCreateModal bool
	showHelp        bool
	sessions        []*api.Session
	selectedSession *api.Session
	err             error
}

// Component represents focusable components in the dashboard
type Component int

const (
	ComponentSummary Component = iota
	ComponentTable
	ComponentDetail
	ComponentModal
)

// NewDashboardModel creates a new dashboard model
func NewDashboardModel(client *api.Client) *DashboardModel {
	// Create and configure help component
	h := help.New()
	h = styles.ConfigureBubblesHelp(h)

	return &DashboardModel{
		client:       client,
		summaryPanel: NewSummaryPanelModel(client),
		sessionTable: NewSessionTableModel(client),
		detailPanel:  NewDetailPanelModel(client),
		createModal:  NewCreateModalModel(client),
		help:         h,
		keys:         DefaultKeyMap(),
		focused:      ComponentTable,
	}
}

// Init implements tea.Model
func (m *DashboardModel) Init() tea.Cmd {
	return tea.Batch(
		m.summaryPanel.Init(),
		m.sessionTable.Init(),
		m.detailPanel.Init(),
		m.loadSessions(), // Load initial data
	)
}

// Update implements tea.Model
func (m *DashboardModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.updateChildSizes()

	case tea.KeyMsg:
		if m.showCreateModal {
			// Handle modal input
			newModal, modalCmd := m.createModal.Update(msg)
			m.createModal = newModal.(*CreateModalModel)
			cmds = append(cmds, modalCmd)

			// Check if modal was closed
			if msg.String() == "esc" || m.createModal.IsCompleted() {
				m.showCreateModal = false
				if m.createModal.IsCompleted() {
					// Refresh sessions after creation
					cmds = append(cmds, m.loadSessions())
				}
				m.createModal.Reset()
			}
		} else {
			// Handle main dashboard input
			switch {
			case key.Matches(msg, m.keys.Quit):
				return m, tea.Quit

			case key.Matches(msg, m.keys.Create):
				m.showCreateModal = true
				m.focused = ComponentModal

			case key.Matches(msg, m.keys.Tab):
				m.cycleFocus()

			case key.Matches(msg, m.keys.Help):
				m.showHelp = !m.showHelp

			case key.Matches(msg, m.keys.Enter):
				if m.focused == ComponentTable && m.selectedSession != nil {
					// Attach to selected session
					return m, m.attachToSession(m.selectedSession.ID)
				}

			case msg.String() == "d":
				if m.focused == ComponentTable && m.selectedSession != nil {
					m.focused = ComponentDetail
				}

			case key.Matches(msg, m.keys.Kill):
				if m.focused == ComponentTable && m.selectedSession != nil {
					return m, m.killSession(m.selectedSession.ID)
				}

			case key.Matches(msg, m.keys.Refresh):
				// Refresh data
				cmds = append(cmds, m.loadSessions())
			}
		}

	case SessionsLoadedMsg:
		m.sessions = msg.Sessions
		m.err = msg.Error
		// Update child components
		m.summaryPanel.SetSessions(msg.Sessions)
		m.sessionTable.SetSessions(msg.Sessions)

	case SessionSelectedMsg:
		m.selectedSession = msg.Session
		m.detailPanel.SetSession(msg.Session)

	case SessionCreatedMsg:
		if msg.Error == nil {
			m.showCreateModal = false
			cmds = append(cmds, m.loadSessions())
		}

	case SessionRefreshedMsg:
		if msg.Session != nil {
			m.selectedSession = msg.Session
			m.detailPanel.SetSession(msg.Session)
		}

	case SessionAttachedMsg:
		if msg.Error != nil {
			m.err = msg.Error
		} else {
			// Successfully attached - exit TUI and let CLI handle the attachment
			return m, tea.Quit
		}

	case SessionKilledMsg:
		if msg.Error != nil {
			m.err = msg.Error
		} else {
			// Successfully killed session - refresh the session list
			cmds = append(cmds, m.loadSessions())
		}

	case RefreshTickMsg:
		// Forward refresh tick to session table for real-time updates
		if m.sessionTable != nil {
			newTable, tableCmd := m.sessionTable.Update(msg)
			m.sessionTable = newTable.(*SessionTableModel)
			cmds = append(cmds, tableCmd)
		}
	}

	// Update child components based on focus
	if !m.showCreateModal {
		switch m.focused {
		case ComponentTable:
			newTable, tableCmd := m.sessionTable.Update(msg)
			m.sessionTable = newTable.(*SessionTableModel)
			cmds = append(cmds, tableCmd)

			// Check for selection changes
			if selected := m.sessionTable.GetSelectedSession(); selected != m.selectedSession {
				m.selectedSession = selected
				m.detailPanel.SetSession(selected)
			}

		case ComponentDetail:
			newDetail, detailCmd := m.detailPanel.Update(msg)
			m.detailPanel = newDetail.(*DetailPanelModel)
			cmds = append(cmds, detailCmd)

		case ComponentSummary:
			newSummary, summaryCmd := m.summaryPanel.Update(msg)
			m.summaryPanel = newSummary.(*SummaryPanelModel)
			cmds = append(cmds, summaryCmd)
		}
	}

	return m, tea.Batch(cmds...)
}

// View implements tea.Model
func (m *DashboardModel) View() string {
	if m.width == 0 || m.height == 0 {
		return "Loading dashboard..."
	}

	// Render main dashboard content
	dashboardContent := m.renderDashboard()

	// Overlay create modal if shown
	if m.showCreateModal {
		modal := m.createModal.View()
		return m.overlayModal(dashboardContent, modal)
	}

	// Overlay help if shown
	if m.showHelp {
		helpView := m.renderHelpView()
		return m.overlayHelp(dashboardContent, helpView)
	}

	return dashboardContent
}

// renderDashboard renders the main dashboard layout with clean 3-section structure
func (m *DashboardModel) renderDashboard() string {
	// Calculate section heights
	headerHeight := 4 // Fixed header height
	footerHeight := 2 // Fixed footer height
	mainHeight := m.height - headerHeight - footerHeight
	if mainHeight < 6 {
		mainHeight = 6 // Minimum main content height
	}

	// Create sections
	header := m.renderHeader(headerHeight)
	mainContent := m.renderMainContent(mainHeight)
	footer := m.renderFooter(footerHeight)

	// Simple vertical layout
	return lipgloss.JoinVertical(
		lipgloss.Left,
		header,
		mainContent,
		footer,
	)
}

// renderHeader renders a clean header section
func (m *DashboardModel) renderHeader(height int) string {
	// Title row with backend info
	title := styles.TitleStyle.Render("Claude Pilot Dashboard")
	backend := styles.SecondaryTextStyle.Render(fmt.Sprintf("Backend: %s", m.client.GetBackend()))

	// Create title row with proper spacing
	titleRow := lipgloss.Place(
		m.width, 1,
		lipgloss.Left, lipgloss.Center,
		lipgloss.JoinHorizontal(lipgloss.Left, title, "  ", backend),
	)

	// Summary section (session stats)
	summaryContent := m.summaryPanel.View()

	// Combine title and summary
	headerContent := lipgloss.JoinVertical(
		lipgloss.Left,
		titleRow,
		summaryContent,
	)

	// Apply header styling and ensure exact height
	return lipgloss.NewStyle().
		Width(m.width).
		Height(height).
		Padding(0, 1).
		Render(headerContent)
}

// renderMainContent renders the main session content area with responsive layout
func (m *DashboardModel) renderMainContent(height int) string {
	// Determine layout based on terminal width
	var content string

	if m.width < 80 {
		// Small screens - show only one panel at a time
		if m.focused == ComponentDetail && m.selectedSession != nil {
			content = m.renderDetailPanel(m.width-2, height-2)
		} else {
			content = m.renderSessionTable(m.width-2, height-2)
		}
	} else if m.width < 120 {
		// Medium screens - two column layout with 2:1 ratio
		leftWidth := (m.width - 6) * 2 / 3 // 2/3 for table
		rightWidth := (m.width - 6) / 3    // 1/3 for detail

		leftPanel := m.renderSessionTable(leftWidth, height-2)

		if m.selectedSession != nil {
			rightPanel := m.renderDetailPanel(rightWidth, height-2)
			content = lipgloss.JoinHorizontal(
				lipgloss.Top,
				leftPanel,
				" ", // Small gap
				rightPanel,
			)
		} else {
			content = leftPanel
		}
	} else {
		// Large screens - equal split layout
		leftWidth := (m.width - 6) / 2
		rightWidth := (m.width - 6) / 2

		leftPanel := m.renderSessionTable(leftWidth, height-2)

		if m.selectedSession != nil {
			rightPanel := m.renderDetailPanel(rightWidth, height-2)
			content = lipgloss.JoinHorizontal(
				lipgloss.Top,
				leftPanel,
				"  ", // Small gap
				rightPanel,
			)
		} else {
			content = leftPanel
		}
	}

	// Apply main content styling
	return lipgloss.NewStyle().
		Width(m.width).
		Height(height).
		Padding(1).
		Render(content)
}

// renderSessionTable renders the session table panel with proper styling
func (m *DashboardModel) renderSessionTable(width, height int) string {
	// Create simple bordered panel for sessions table
	borderColor := styles.BackgroundSurface
	if m.focused == ComponentTable {
		borderColor = styles.ClaudePrimary
	}

	panelStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(borderColor).
		Padding(1).
		Width(width).
		Height(height)

	// Add title
	titleStyle := lipgloss.NewStyle().
		Foreground(styles.ClaudePrimary).
		Bold(true).
		Padding(0, 1)

	title := titleStyle.Render("Sessions")
	content := lipgloss.JoinVertical(lipgloss.Left, title, m.sessionTable.View())

	return panelStyle.Render(content)
}

// renderDetailPanel renders the detail panel with proper styling
func (m *DashboardModel) renderDetailPanel(width, height int) string {
	// Create simple bordered panel for detail view
	borderColor := styles.BackgroundSurface
	if m.focused == ComponentDetail {
		borderColor = styles.ClaudePrimary
	}

	panelStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(borderColor).
		Padding(1).
		Width(width).
		Height(height)

	// Add title
	titleStyle := lipgloss.NewStyle().
		Foreground(styles.ClaudePrimary).
		Bold(true).
		Padding(0, 1)

	title := titleStyle.Render("Session Details")
	content := lipgloss.JoinVertical(lipgloss.Left, title, m.detailPanel.View())

	return panelStyle.Render(content)
}

// renderFooter renders a compact footer with essential keyboard shortcuts
func (m *DashboardModel) renderFooter(height int) string {
	var shortcuts string

	if m.showCreateModal {
		// Modal shortcuts
		shortcuts = "Enter: Create • Esc: Cancel"
	} else {
		// Main dashboard shortcuts based on focused component
		switch m.focused {
		case ComponentTable:
			shortcuts = "↑↓/jk: navigate • Enter: attach • n/c: create • d/x: kill • r: refresh • ?: help • q: quit"
		case ComponentDetail:
			shortcuts = "↑↓/jk: scroll • PgUp/PgDn: page • g/G: top/bottom • Tab: focus table • q: quit"
		default:
			shortcuts = "Tab: cycle focus • ?: help • q: quit"
		}
	}

	// Center the shortcuts in the footer
	footerContent := lipgloss.Place(
		m.width, height,
		lipgloss.Center, lipgloss.Center,
		styles.FooterStyle.Render(shortcuts),
	)

	return footerContent
}

// overlayModal overlays the create modal over the dashboard content using flexbox
func (m *DashboardModel) overlayModal(background, modal string) string {
	// Modal dimensions - increased width for better form layout
	modalWidth := 80
	modalHeight := 18

	// Create modal content with styling
	overlay := lipgloss.NewStyle().
		Width(modalWidth).
		Height(modalHeight).
		Border(lipgloss.RoundedBorder()).
		BorderForeground(styles.ClaudePrimary)
		// Background(styles.BackgroundPrimary)

	styledModal := overlay.Render(modal)

	// Overlay the modal on top of the background using lipgloss.Place
	return lipgloss.Place(
		m.width, m.height,
		lipgloss.Center, lipgloss.Center,
		styledModal,
		lipgloss.WithWhitespaceChars(" "),
		lipgloss.WithWhitespaceForeground(styles.BackgroundSecondary),
		lipgloss.WithWhitespaceBackground(lipgloss.Color(background)),
	)
}

// renderHelpView renders the help overlay content
func (m *DashboardModel) renderHelpView() string {
	var content strings.Builder

	// Title
	content.WriteString(styles.TitleStyle.Render("Keyboard Shortcuts"))
	content.WriteString("\n\n")

	// Main shortcuts
	content.WriteString(styles.HeaderStyle.Render("Main Controls"))
	content.WriteString("\n")
	content.WriteString(m.help.View(m.keys))
	content.WriteString("\n\n")

	// Context-specific shortcuts
	switch m.focused {
	case ComponentTable:
		content.WriteString(styles.HeaderStyle.Render("Table Navigation"))
		content.WriteString("\n")
		content.WriteString(m.help.View(m.keys))
	case ComponentDetail:
		content.WriteString(styles.HeaderStyle.Render("Detail Panel"))
		content.WriteString("\n")
		content.WriteString(m.help.View(m.keys))
	}

	content.WriteString("\n\n")
	content.WriteString(styles.DimTextStyle.Render("Press ? again to close help"))

	return content.String()
}

// overlayHelp overlays the help content over the dashboard using simple centering
func (m *DashboardModel) overlayHelp(background, helpContent string) string {
	// Help overlay dimensions
	helpWidth := 60
	helpHeight := 20

	// Create help overlay style
	overlay := lipgloss.NewStyle().
		Width(helpWidth).
		Height(helpHeight).
		Border(lipgloss.RoundedBorder()).
		BorderForeground(styles.InfoColor).
		Background(styles.BackgroundPrimary).
		Padding(1)

	styledHelp := overlay.Render(helpContent)

	// Use lipgloss.Place for simple centering
	return lipgloss.Place(
		m.width, m.height,
		lipgloss.Center, lipgloss.Center,
		styledHelp,
		lipgloss.WithWhitespaceChars(" "),
		lipgloss.WithWhitespaceForeground(styles.BackgroundSecondary),
	)
}

// updateChildSizes updates the sizes of child components
func (m *DashboardModel) updateChildSizes() {
	// Calculate section heights
	headerHeight := 4
	footerHeight := 2
	mainHeight := m.height - headerHeight - footerHeight
	if mainHeight < 6 {
		mainHeight = 6
	}

	// Update child component sizes
	m.summaryPanel.SetSize(m.width-2, 2) // Compact summary in header
	m.sessionTable.SetSize(m.width/2, mainHeight-2)
	m.detailPanel.SetSize(m.width/2, mainHeight-2)
	m.createModal.SetSize(min(80, m.width-10), min(18, m.height-6)) // Responsive modal size
}

// cycleFocus cycles through focusable components
func (m *DashboardModel) cycleFocus() {
	switch m.focused {
	case ComponentSummary:
		m.focused = ComponentTable
	case ComponentTable:
		if m.selectedSession != nil {
			m.focused = ComponentDetail
		} else {
			m.focused = ComponentSummary
		}
	case ComponentDetail:
		m.focused = ComponentSummary
	default:
		m.focused = ComponentTable
	}
}

// loadSessions loads sessions from the API
func (m *DashboardModel) loadSessions() tea.Cmd {
	return func() tea.Msg {
		sessions, err := m.client.ListSessions()
		return SessionsLoadedMsg{Sessions: sessions, Error: err}
	}
}

// attachToSession attaches to a session
func (m *DashboardModel) attachToSession(sessionID string) tea.Cmd {
	return func() tea.Msg {
		err := m.client.AttachToSession(sessionID)
		return SessionAttachedMsg{
			SessionID: sessionID,
			Error:     err,
		}
	}
}

// killSession kills a session
func (m *DashboardModel) killSession(sessionID string) tea.Cmd {
	return func() tea.Msg {
		err := m.client.KillSession(sessionID)
		return SessionKilledMsg{
			SessionID: sessionID,
			Error:     err,
		}
	}
}

// SetSize updates the dashboard size
func (m *DashboardModel) SetSize(width, height int) {
	m.width = width
	m.height = height
	m.updateChildSizes()
}

// Common message types
type SessionsLoadedMsg struct {
	Sessions []*api.Session
	Error    error
}

type ErrorMsg struct {
	Error error
}

type SessionAttachedMsg struct {
	SessionID string
	Error     error
}

type SessionKilledMsg struct {
	SessionID string
	Error     error
}

// RefreshTickMsg represents a periodic refresh tick
type RefreshTickMsg struct {
	Time time.Time
}
